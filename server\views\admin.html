<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepChat 管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #333;
            font-size: 24px;
        }

        .tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .tab-button {
            padding: 10px 20px;
            background: #e9ecef;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
        }

        .tab-button.active {
            background: #007bff;
            color: white;
        }

        .search-box {
            display: flex;
            gap: 10px;
        }

        .search-box input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .search-box button {
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .controls {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
        }

        .controls button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #555;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .file-name, .user-name {
            font-weight: 500;
            color: #333;
        }

        .file-size, .user-email {
            color: #666;
            font-size: 14px;
        }

        .actions {
            display: flex;
            gap: 8px;
        }

        .actions button {
            padding: 4px 8px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .btn-sm-primary {
            background: #007bff;
            color: white;
        }

        .btn-sm-danger {
            background: #dc3545;
            color: white;
        }

        .btn-sm-warning {
            background: #ffc107;
            color: #212529;
        }

        .pagination {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            gap: 10px;
        }

        .pagination-controls button {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
        }

        .pagination-controls button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            background: white;
            margin: 100px auto;
            padding: 20px;
            border-radius: 8px;
            max-width: 500px;
            position: relative;
        }

        .close {
            position: absolute;
            right: 15px;
            top: 15px;
            font-size: 24px;
            cursor: pointer;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group input, .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .alert {
            padding: 12px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .hidden {
            display: none;
        }

        .upload-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s;
        }

        .upload-area:hover {
            border-color: #007bff;
        }

        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }

        .upload-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }

        .file-list {
            margin-top: 20px;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }

        .file-info {
            flex: 1;
        }

        .file-progress {
            width: 100%;
            height: 4px;
            background: #e9ecef;
            border-radius: 2px;
            overflow: hidden;
            margin-top: 5px;
        }

        .file-progress-bar {
            height: 100%;
            background: #007bff;
            width: 0%;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>_deepChat 管理后台</h1>
            <div class="search-box">
                <input type="text" id="searchInput" placeholder="搜索...">
                <button id="searchBtn">搜索</button>
            </div>
        </div>

        <div class="tabs">
            <button class="tab-button active" data-tab="files">文件管理</button>
            <button class="tab-button" data-tab="users">用户管理</button>
            <button class="tab-button" data-tab="upload">文件上传</button>
        </div>

        <div id="alertContainer"></div>

        <!-- 文件管理页面 -->
        <div id="filesTab" class="tab-content">
            <div class="controls">
                <button class="btn-primary" id="refreshFilesBtn">刷新列表</button>
            </div>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>文件名</th>
                            <th>大小</th>
                            <th>上传时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="filesTableBody">
                        <!-- 文件列表将通过JavaScript动态填充 -->
                    </tbody>
                </table>
            </div>

            <div class="pagination">
                <div class="pagination-info" id="filesPaginationInfo">
                    显示第 1 到 10 项，共 0 项
                </div>
                <div class="pagination-controls">
                    <button id="filesPrevBtn" disabled>上一页</button>
                    <button id="filesNextBtn" disabled>下一页</button>
                </div>
            </div>
        </div>

        <!-- 用户管理页面 -->
        <div id="usersTab" class="tab-content hidden">
            <div class="controls">
                <button class="btn-primary" id="refreshUsersBtn">刷新列表</button>
            </div>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>用户名</th>
                            <th>邮箱</th>
                            <th>注册时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="usersTableBody">
                        <!-- 用户列表将通过JavaScript动态填充 -->
                    </tbody>
                </table>
            </div>

            <div class="pagination">
                <div class="pagination-info" id="usersPaginationInfo">
                    显示第 1 到 10 项，共 0 项
                </div>
                <div class="pagination-controls">
                    <button id="usersPrevBtn" disabled>上一页</button>
                    <button id="usersNextBtn" disabled>下一页</button>
                </div>
            </div>
        </div>

        <!-- 文件上传页面 -->
        <div id="uploadTab" class="tab-content hidden">
            <div class="upload-section">
                <h2>上传文件</h2>
                <div class="upload-area" id="uploadArea">
                    <p>点击选择文件或拖拽文件到此处</p>
                    <p>支持多种格式的文件上传</p>
                    <button class="upload-btn" id="selectFileBtn">选择文件</button>
                    <input type="file" id="fileInput" multiple style="display: none;">
                </div>
                <div class="file-list" id="fileList"></div>
            </div>
        </div>
    </div>

    <!-- 编辑文件模态框 -->
    <div id="editFileModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>编辑文件信息</h2>
            <form id="editFileForm">
                <input type="hidden" id="editFileId">
                <div class="form-group">
                    <label for="editFileName">文件名:</label>
                    <input type="text" id="editFileName" required>
                </div>
                <div class="form-actions">
                    <button type="button" id="cancelEditFileBtn">取消</button>
                    <button type="submit" class="btn-success">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 编辑用户模态框 -->
    <div id="editUserModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>编辑用户信息</h2>
            <form id="editUserForm">
                <input type="hidden" id="editUserId">
                <div class="form-group">
                    <label for="editUsername">用户名:</label>
                    <input type="text" id="editUsername" required>
                </div>
                <div class="form-group">
                    <label for="editUserEmail">邮箱:</label>
                    <input type="email" id="editUserEmail" required>
                </div>
                <div class="form-group">
                    <label for="editUserAvatar">头像URL:</label>
                    <input type="text" id="editUserAvatar">
                </div>
                <div class="form-actions">
                    <button type="button" id="cancelEditUserBtn">取消</button>
                    <button type="submit" class="btn-success">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTab = 'files';
        let currentPage = { files: 1, users: 1 };
        let currentSearch = '';
        let totalPages = { files: 0, users: 0 };
        let totalItems = { files: 0, users: 0 };

        // DOM元素
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.getElementById('searchBtn');
        const alertContainer = document.getElementById('alertContainer');

        // 文件管理相关元素
        const filesTableBody = document.getElementById('filesTableBody');
        const filesPaginationInfo = document.getElementById('filesPaginationInfo');
        const filesPrevBtn = document.getElementById('filesPrevBtn');
        const filesNextBtn = document.getElementById('filesNextBtn');
        const refreshFilesBtn = document.getElementById('refreshFilesBtn');

        // 用户管理相关元素
        const usersTableBody = document.getElementById('usersTableBody');
        const usersPaginationInfo = document.getElementById('usersPaginationInfo');
        const usersPrevBtn = document.getElementById('usersPrevBtn');
        const usersNextBtn = document.getElementById('usersNextBtn');
        const refreshUsersBtn = document.getElementById('refreshUsersBtn');

        // 文件上传相关元素
        const uploadArea = document.getElementById('uploadArea');
        const selectFileBtn = document.getElementById('selectFileBtn');
        const fileInput = document.getElementById('fileInput');
        const fileList = document.getElementById('fileList');

        // 模态框相关元素
        const editFileModal = document.getElementById('editFileModal');
        const editFileForm = document.getElementById('editFileForm');
        const editUserModal = document.getElementById('editUserModal');
        const editUserForm = document.getElementById('editUserForm');
        const closeButtons = document.querySelectorAll('.close');
        const cancelEditFileBtn = document.getElementById('cancelEditFileBtn');
        const cancelEditUserBtn = document.getElementById('cancelEditUserBtn');

        // 显示提示信息
        function showAlert(message, type) {
            alertContainer.innerHTML = `
                <div class="alert alert-${type}">
                    ${message}
                </div>
            `;
            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 3000);
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 格式化日期
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        // 切换标签页
        function switchTab(tabName) {
            // 更新标签按钮状态
            tabButtons.forEach(button => {
                if (button.dataset.tab === tabName) {
                    button.classList.add('active');
                } else {
                    button.classList.remove('active');
                }
            });

            // 显示对应的标签内容
            tabContents.forEach(content => {
                if (content.id === tabName + 'Tab') {
                    content.classList.remove('hidden');
                } else {
                    content.classList.add('hidden');
                }
            });

            currentTab = tabName;

            // 根据当前标签页刷新数据
            if (tabName === 'files') {
                fetchFiles(currentPage.files, currentSearch);
            } else if (tabName === 'users') {
                fetchUsers(currentPage.users, currentSearch);
            }
        }

        // 获取文件列表
        async function fetchFiles(page = 1, search = '') {
            try {
                const response = await fetch(`/api/admin/files?page=${page}&limit=10&search=${encodeURIComponent(search)}`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });
                
                if (!response.ok) {
                    throw new Error('获取文件列表失败');
                }
                
                const result = await response.json();
                const files = result.data.files;
                totalPages.files = result.data.pagination.pages;
                totalItems.files = result.data.pagination.total;
                
                renderFiles(files);
                updateFilesPagination();
            } catch (error) {
                showAlert('获取文件列表失败: ' + error.message, 'error');
            }
        }

        // 渲染文件列表
        function renderFiles(files) {
            filesTableBody.innerHTML = '';
            
            if (files.length === 0) {
                filesTableBody.innerHTML = `
                    <tr>
                        <td colspan="4" style="text-align: center; color: #666;">暂无文件</td>
                    </tr>
                `;
                return;
            }
            
            files.forEach(file => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div class="file-name">${file.originalName}</div>
                        <div class="file-size">${file.filename}</div>
                    </td>
                    <td>${formatFileSize(file.size)}</td>
                    <td>${formatDate(file.createdAt)}</td>
                    <td class="actions">
                        <button class="btn-sm-primary" onclick="viewFile('${file.url}')">查看</button>
                        <button class="btn-sm-warning" onclick="editFile('${file._id}', '${file.originalName}')">编辑</button>
                        <button class="btn-sm-danger" onclick="deleteFile('${file._id}', '${file.originalName}')">删除</button>
                    </td>
                `;
                filesTableBody.appendChild(row);
            });
        }

        // 更新文件分页信息
        function updateFilesPagination() {
            const start = totalItems.files > 0 ? (currentPage.files - 1) * 10 + 1 : 0;
            const end = Math.min(currentPage.files * 10, totalItems.files);
            
            filesPaginationInfo.textContent = `显示第 ${start} 到 ${end} 项，共 ${totalItems.files} 项`;
            
            filesPrevBtn.disabled = currentPage.files <= 1;
            filesNextBtn.disabled = currentPage.files >= totalPages.files;
        }

        // 获取用户列表
        async function fetchUsers(page = 1, search = '') {
            try {
                const response = await fetch(`/api/admin/users?page=${page}&limit=10&search=${encodeURIComponent(search)}`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });
                
                if (!response.ok) {
                    throw new Error('获取用户列表失败');
                }
                
                const result = await response.json();
                const users = result.data.users;
                totalPages.users = result.data.pagination.pages;
                totalItems.users = result.data.pagination.total;
                
                renderUsers(users);
                updateUsersPagination();
            } catch (error) {
                showAlert('获取用户列表失败: ' + error.message, 'error');
            }
        }

        // 渲染用户列表
        function renderUsers(users) {
            usersTableBody.innerHTML = '';
            
            if (users.length === 0) {
                usersTableBody.innerHTML = `
                    <tr>
                        <td colspan="4" style="text-align: center; color: #666;">暂无用户</td>
                    </tr>
                `;
                return;
            }
            
            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div class="user-name">${user.username}</div>
                    </td>
                    <td>
                        <div class="user-email">${user.email}</div>
                    </td>
                    <td>${formatDate(user.createdAt)}</td>
                    <td class="actions">
                        <button class="btn-sm-warning" onclick="editUser('${user._id}', '${user.username}', '${user.email}', '${user.avatar || ''}')">编辑</button>
                        <button class="btn-sm-danger" onclick="deleteUser('${user._id}', '${user.username}')">删除</button>
                    </td>
                `;
                usersTableBody.appendChild(row);
            });
        }

        // 更新用户分页信息
        function updateUsersPagination() {
            const start = totalItems.users > 0 ? (currentPage.users - 1) * 10 + 1 : 0;
            const end = Math.min(currentPage.users * 10, totalItems.users);
            
            usersPaginationInfo.textContent = `显示第 ${start} 到 ${end} 项，共 ${totalItems.users} 项`;
            
            usersPrevBtn.disabled = currentPage.users <= 1;
            usersNextBtn.disabled = currentPage.users >= totalPages.users;
        }

        // 查看文件
        function viewFile(fileUrl) {
            // 在新窗口中打开文件
            window.open(fileUrl, '_blank');
        }

        // 编辑文件
        function editFile(fileId, fileName) {
            document.getElementById('editFileId').value = fileId;
            document.getElementById('editFileName').value = fileName;
            editFileModal.style.display = 'block';
        }

        // 删除文件
        async function deleteFile(fileId, fileName) {
            if (!confirm(`确定要删除文件 "${fileName}" 吗？`)) {
                return;
            }
            
            try {
                const response = await fetch(`/api/admin/files/${fileId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });
                
                if (!response.ok) {
                    throw new Error('删除文件失败');
                }
                
                showAlert('文件删除成功', 'success');
                fetchFiles(currentPage.files, currentSearch);
            } catch (error) {
                showAlert('删除文件失败: ' + error.message, 'error');
            }
        }

        // 编辑用户
        function editUser(userId, username, email, avatar) {
            document.getElementById('editUserId').value = userId;
            document.getElementById('editUsername').value = username;
            document.getElementById('editUserEmail').value = email;
            document.getElementById('editUserAvatar').value = avatar || '';
            editUserModal.style.display = 'block';
        }

        // 删除用户
        async function deleteUser(userId, username) {
            if (!confirm(`确定要删除用户 "${username}" 吗？`)) {
                return;
            }
            
            try {
                const response = await fetch(`/api/admin/users/${userId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });
                
                if (!response.ok) {
                    throw new Error('删除用户失败');
                }
                
                showAlert('用户删除成功', 'success');
                fetchUsers(currentPage.users, currentSearch);
            } catch (error) {
                showAlert('删除用户失败: ' + error.message, 'error');
            }
        }

        // 上传文件
        function uploadFiles(files) {
            fileList.innerHTML = '';
            
            Array.from(files).forEach(file => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div class="file-info">
                        <div>${file.name}</div>
                        <div>${formatFileSize(file.size)}</div>
                        <div class="file-progress">
                            <div class="file-progress-bar" id="progress-${file.name}"></div>
                        </div>
                    </div>
                    <div id="status-${file.name}">等待上传</div>
                `;
                fileList.appendChild(fileItem);
                
                // 执行上传
                uploadFile(file, file.name);
            });
        }

        // 上传单个文件
        async function uploadFile(file, fileName) {
            try {
                const statusElement = document.getElementById(`status-${fileName}`);
                const progressBar = document.getElementById(`progress-${fileName}`);
                
                statusElement.textContent = '上传中...';
                
                const formData = new FormData();
                formData.append('file', file);
                
                const xhr = new XMLHttpRequest();
                
                // 更新进度条
                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        progressBar.style.width = percentComplete + '%';
                    }
                });
                
                // 处理上传完成
                xhr.addEventListener('load', () => {
                    if (xhr.status === 200) {
                        const response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            statusElement.textContent = '上传成功';
                            statusElement.style.color = 'green';
                            // 刷新文件列表
                            if (currentTab === 'files') {
                                fetchFiles(currentPage.files, currentSearch);
                            }
                        } else {
                            statusElement.textContent = '上传失败: ' + response.message;
                            statusElement.style.color = 'red';
                        }
                    } else {
                        statusElement.textContent = '上传失败';
                        statusElement.style.color = 'red';
                    }
                });
                
                // 处理上传错误
                xhr.addEventListener('error', () => {
                    statusElement.textContent = '上传失败';
                    statusElement.style.color = 'red';
                });
                
                xhr.open('POST', '/api/files/upload');
                xhr.setRequestHeader('Authorization', `Bearer ${localStorage.getItem('token')}`);
                xhr.send(formData);
            } catch (error) {
                console.error('上传文件失败:', error);
                document.getElementById(`status-${fileName}`).textContent = '上传失败: ' + error.message;
                document.getElementById(`status-${fileName}`).style.color = 'red';
            }
        }

        // 事件监听器
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                switchTab(button.dataset.tab);
            });
        });

        searchBtn.addEventListener('click', () => {
            currentSearch = searchInput.value.trim();
            currentPage[currentTab] = 1;
            if (currentTab === 'files') {
                fetchFiles(currentPage.files, currentSearch);
            } else if (currentTab === 'users') {
                fetchUsers(currentPage.users, currentSearch);
            }
        });

        refreshFilesBtn.addEventListener('click', () => {
            fetchFiles(currentPage.files, currentSearch);
        });

        refreshUsersBtn.addEventListener('click', () => {
            fetchUsers(currentPage.users, currentSearch);
        });

        filesPrevBtn.addEventListener('click', () => {
            if (currentPage.files > 1) {
                currentPage.files--;
                fetchFiles(currentPage.files, currentSearch);
            }
        });

        filesNextBtn.addEventListener('click', () => {
            if (currentPage.files < totalPages.files) {
                currentPage.files++;
                fetchFiles(currentPage.files, currentSearch);
            }
        });

        usersPrevBtn.addEventListener('click', () => {
            if (currentPage.users > 1) {
                currentPage.users--;
                fetchUsers(currentPage.users, currentSearch);
            }
        });

        usersNextBtn.addEventListener('click', () => {
            if (currentPage.users < totalPages.users) {
                currentPage.users++;
                fetchUsers(currentPage.users, currentSearch);
            }
        });

        editFileForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const fileId = document.getElementById('editFileId').value;
            const fileName = document.getElementById('editFileName').value;
            
            try {
                const response = await fetch(`/api/admin/files/${fileId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify({ originalName: fileName })
                });
                
                if (!response.ok) {
                    throw new Error('更新文件信息失败');
                }
                
                showAlert('文件信息更新成功', 'success');
                editFileModal.style.display = 'none';
                fetchFiles(currentPage.files, currentSearch);
            } catch (error) {
                showAlert('更新文件信息失败: ' + error.message, 'error');
            }
        });

        editUserForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const userId = document.getElementById('editUserId').value;
            const username = document.getElementById('editUsername').value;
            const email = document.getElementById('editUserEmail').value;
            const avatar = document.getElementById('editUserAvatar').value;
            
            try {
                const response = await fetch(`/api/admin/users/${userId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify({ username, email, avatar })
                });
                
                if (!response.ok) {
                    throw new Error('更新用户信息失败');
                }
                
                showAlert('用户信息更新成功', 'success');
                editUserModal.style.display = 'none';
                fetchUsers(currentPage.users, currentSearch);
            } catch (error) {
                showAlert('更新用户信息失败: ' + error.message, 'error');
            }
        });

        closeButtons.forEach(button => {
            button.addEventListener('click', () => {
                editFileModal.style.display = 'none';
                editUserModal.style.display = 'none';
            });
        });

        cancelEditFileBtn.addEventListener('click', () => {
            editFileModal.style.display = 'none';
        });

        cancelEditUserBtn.addEventListener('click', () => {
            editUserModal.style.display = 'none';
        });

        window.addEventListener('click', (e) => {
            if (e.target === editFileModal) {
                editFileModal.style.display = 'none';
            }
            if (e.target === editUserModal) {
                editUserModal.style.display = 'none';
            }
        });

        // 文件上传相关事件
        selectFileBtn.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                uploadFiles(e.target.files);
            }
        });

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            if (e.dataTransfer.files.length > 0) {
                uploadFiles(e.dataTransfer.files);
            }
        });

        // 搜索框回车事件
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                searchBtn.click();
            }
        });

        // 检查用户认证状态
        function checkAuthentication() {
            const token = localStorage.getItem('token');
            if (!token) {
                // 没有token，重定向到登录页面
                window.location.href = '/admin/login';
                return false;
            }
            return true;
        }

        // 验证token有效性
        async function validateToken() {
            const token = localStorage.getItem('token');
            if (!token) {
                window.location.href = '/admin/login';
                return false;
            }

            try {
                // 尝试调用一个需要认证的API来验证token
                const response = await fetch('/api/admin/users?page=1&limit=1', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (!response.ok) {
                    // token无效，清除并重定向
                    localStorage.removeItem('token');
                    window.location.href = '/admin/login';
                    return false;
                }

                return true;
            } catch (error) {
                // 网络错误或其他问题，清除token并重定向
                localStorage.removeItem('token');
                window.location.href = '/admin/login';
                return false;
            }
        }

        // 页面加载时检查认证并获取数据
        document.addEventListener('DOMContentLoaded', async () => {
            // 首先检查是否有token
            if (!checkAuthentication()) {
                return;
            }

            // 验证token有效性
            const isValid = await validateToken();
            if (!isValid) {
                return;
            }

            // token有效，加载数据
            fetchFiles();
        });
    </script>
</body>
</html>