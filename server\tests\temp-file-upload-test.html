<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>临时文件上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .file-info {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>临时文件上传测试</h1>
    <p>此页面用于测试临时文件上传功能（免token）</p>
    
    <div class="upload-area" id="uploadArea">
        <p>点击选择文件或拖拽文件到此处</p>
        <input type="file" id="fileInput" style="display: none;" multiple>
        <button class="btn" id="selectFileBtn">选择文件</button>
    </div>
    
    <div id="fileList"></div>
    <div id="messages"></div>
    
    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const selectFileBtn = document.getElementById('selectFileBtn');
        const fileList = document.getElementById('fileList');
        const messages = document.getElementById('messages');
        
        // 显示消息
        function showMessage(message, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = message;
            messages.appendChild(messageDiv);
            
            setTimeout(() => {
                messageDiv.remove();
            }, 5000);
        }
        
        // 选择文件按钮点击事件
        selectFileBtn.addEventListener('click', () => {
            fileInput.click();
        });
        
        // 上传区域点击事件
        uploadArea.addEventListener('click', (e) => {
            if (e.target !== selectFileBtn) {
                fileInput.click();
            }
        });
        
        // 文件选择事件
        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });
        
        // 拖拽事件
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#007bff';
            uploadArea.style.backgroundColor = '#f8f9fa';
        });
        
        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = 'transparent';
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = 'transparent';
            handleFiles(e.dataTransfer.files);
        });
        
        // 处理文件
        function handleFiles(files) {
            fileList.innerHTML = '';
            
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                uploadFile(file);
            }
        }
        
        // 上传文件
        async function uploadFile(file) {
            const fileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
            
            // 创建文件信息显示
            const fileDiv = document.createElement('div');
            fileDiv.className = 'file-info';
            fileDiv.innerHTML = `
                <strong>${file.name}</strong> (${formatFileSize(file.size)})
                <br>
                <span id="status-${fileName}">准备上传...</span>
                <div style="background-color: #e9ecef; height: 10px; border-radius: 5px; margin-top: 5px;">
                    <div id="progress-${fileName}" style="background-color: #007bff; height: 100%; border-radius: 5px; width: 0%; transition: width 0.3s;"></div>
                </div>
            `;
            fileList.appendChild(fileDiv);
            
            try {
                const statusElement = document.getElementById(`status-${fileName}`);
                const progressBar = document.getElementById(`progress-${fileName}`);
                
                statusElement.textContent = '上传中...';
                
                const formData = new FormData();
                formData.append('file', file);
                
                const xhr = new XMLHttpRequest();
                
                // 更新进度条
                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        progressBar.style.width = percentComplete + '%';
                    }
                });
                
                // 处理上传完成
                xhr.addEventListener('load', () => {
                    if (xhr.status === 200) {
                        const response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            statusElement.textContent = '上传成功';
                            statusElement.style.color = 'green';
                            showMessage(`
                                <strong>临时文件上传成功!</strong><br>
                                URL: <a href="${response.data.url}" target="_blank">${response.data.url}</a><br>
                                文件ID: ${response.data.fileId}<br>
                                消息: ${response.message}
                            `, 'success');
                        } else {
                            statusElement.textContent = '上传失败: ' + response.message;
                            statusElement.style.color = 'red';
                            showMessage(`上传失败: ${response.message}`, 'error');
                        }
                    } else {
                        statusElement.textContent = '上传失败';
                        statusElement.style.color = 'red';
                        showMessage('上传失败: HTTP ' + xhr.status, 'error');
                    }
                });
                
                // 处理上传错误
                xhr.addEventListener('error', () => {
                    statusElement.textContent = '上传失败';
                    statusElement.style.color = 'red';
                    showMessage('上传失败: 网络错误', 'error');
                });
                
                // 注意：这里使用临时文件上传接口，不需要token
                xhr.open('POST', '/api/files/temp-upload');
                xhr.send(formData);
            } catch (error) {
                console.error('上传文件失败:', error);
                document.getElementById(`status-${fileName}`).textContent = '上传失败: ' + error.message;
                document.getElementById(`status-${fileName}`).style.color = 'red';
                showMessage('上传失败: ' + error.message, 'error');
            }
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
